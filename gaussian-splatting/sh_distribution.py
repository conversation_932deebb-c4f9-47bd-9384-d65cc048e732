#
# Copyright (C) 2023, Inria
# GRAPHDECO research group, https://team.inria.fr/graphdeco
# All rights reserved.
#
# This software is free for non-commercial, research and evaluation use 
# under the terms of the LICENSE.md file.
#
# For inquiries contact  george.d<PERSON><PERSON>@inria.fr
#

import torch
from scene import Scene
import os
from tqdm import tqdm
from os import makedirs
from gaussian_renderer import render, network_gui
import torchvision
from utils.general_utils import safe_state
from argparse import ArgumentParser
from arguments import ModelParams, PipelineParams, get_combined_args
from gaussian_renderer import GaussianModel
import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import pearsonr
try:
    from diff_gaussian_rasterization import SparseGaussianAdam
    SPARSE_ADAM_AVAILABLE = True
except:
    SPARSE_ADAM_AVAILABLE = False

def analyze_features_rest(shs):
    """
    分析_features_rest属性的两个特性:
    1. 通道间相关性分布
    2. 稀疏性分布
    
    参数:
        shs: 形状为[num_gaussians, 3, 15]的张量
    """
    num_gaussians = shs.shape[0]

    
    # 1. 计算通道间相关性分布
    print("计算通道间相关性...")
    norms = torch.norm(shs, dim=2, keepdim=True)
    normalized = shs / (norms + 1e-8)  # [n, 3, 15]
    
    # 方法1: 成对余弦相似度矩阵
    cosine_sim = torch.bmm(normalized, normalized.transpose(1,2))  # [n, 3, 3]
    
    # 2. 计算每个高斯的稀疏性
    print("计算稀疏性...")
    # 使用L1范数/L2范数作为稀疏性度量
    sparse_measures = []
    
    
    # 绘制结果
    plt.figure(figsize=(12, 5))
    
    # 通道相关性分布
    plt.subplot(1, 2, 1)
    plt.hist(channel_corrs, bins=50, alpha=0.7)
    plt.title('Channel-wise Correlation Distribution')
    plt.xlabel('Pearson Correlation Coefficient')
    plt.ylabel('Frequency')
    
    # 稀疏性分布
    plt.subplot(1, 2, 2)
    plt.hist(sparse_measures, bins=50, alpha=0.7)
    plt.title('Per-Gaussian Sparsity Distribution')
    plt.xlabel('Sparsity Measure (L1/L2)')
    plt.ylabel('Frequency')
    
    plt.tight_layout()
    plt.show()
    
    return channel_corrs, sparse_measures

def get_distribution(dataset: ModelParams, iteration: int):
    with torch.no_grad():
        gaussians = GaussianModel(dataset.sh_degree)
        scene = Scene(dataset, gaussians, load_iteration=iteration, shuffle=False)
        shs = gaussians._features_rest  # shape: [num_gaussians, 3, 15]
        
        # 分析_features_rest属性
        channel_corrs, sparse_measures = analyze_features_rest(shs)
        
        return channel_corrs, sparse_measures
        

if __name__ == "__main__":
    # Set up command line argument parser
    parser = ArgumentParser(description="Testing script parameters")
    model = ModelParams(parser, sentinel=True)
    pipeline = PipelineParams(parser)
    parser.add_argument("--iteration", default=-1, type=int)
    parser.add_argument("--skip_train", action="store_true")
    parser.add_argument("--skip_test", action="store_true")
    parser.add_argument("--quiet", action="store_true")
    args = get_combined_args(parser)
    print("Rendering " + args.model_path)

    # Initialize system state (RNG)
    safe_state(args.quiet)

    get_distribution(model.extract(args), args.iteration)