# Sharpness-based Spherical Gaussian Culling Implementation

## 概述

本实现为球面高斯3D Gaussian Splatting添加了基于sharpness的optimizing-sparsifying处理方式，类似于opacity的处理方法。当sharpness小到一定程度时，系统会将该轴的RGB加到Base RGB上，然后删除这条轴。

## 主要功能

### 1. 动态轴数管理
- 每个gaussian都记录自己的有效球面高斯基轴数
- 支持动态删除低sharpness的轴
- 自动更新相关的优化器状态

### 2. Sharpness剪枝
- 当轴的sharpness低于阈值时，将该轴的RGB贡献加到Base RGB上
- 删除该轴并重新组织剩余轴的数据
- 保持数据的连续性和一致性

### 3. 优化器集成
- 修复了`OptimizingSpaSG`中的维度处理问题
- 支持球面高斯的三维张量结构 `[N, max_sg_degree, 1]`
- 正确处理重要性分数的计算和剪枝

## 修改的文件

### 1. `optimizing_spa_sg.py`
- **修复**: `prune_z_metrics_imp_score`函数的维度处理
- **改进**: 正确处理球面高斯的三维张量结构
- **修复**: 属性访问错误（`get_sg_sharpness()`改为`get_sg_sharpness`）

### 2. `scene/spherical_gaussian_model_cullSG.py`
- **新增**: `_sg_axis_count`属性记录每个gaussian的有效轴数
- **新增**: `cull_low_sharpness_axes()`方法实现sharpness剪枝
- **新增**: `get_sg_axis_count`属性访问器
- **修改**: `compute_colors_precomp()`支持动态轴数的颜色计算
- **修改**: 所有相关函数支持轴数记录的管理

### 3. `train_imp_score_spherical_gaussian_cullSG.py`
- **完善**: sharpness剪枝的训练逻辑
- **修复**: `update_sg_imp_score()`函数正确计算球面高斯重要性分数
- **新增**: 命令行参数`--cull_SG`和`--sharpness_threshold`

## 使用方法

### 训练参数
```bash
python train_imp_score_spherical_gaussian_cullSG.py \
    --source_path /path/to/data \
    --model_path /path/to/output \
    --imp_metric outdoor \
    --cull_SG 30000 35000 40000 \
    --sharpness_threshold 0.01
```

### 关键参数说明
- `--cull_SG`: 执行sharpness剪枝的迭代次数列表
- `--sharpness_threshold`: sharpness阈值，低于此值的轴将被剪枝

## 核心算法

### Sharpness剪枝流程
1. 检测低于阈值的sharpness轴
2. 将该轴的RGB贡献加到Base RGB上
3. 将后续轴的数据前移填补空隙
4. 更新该gaussian的有效轴数
5. 清零最后一个轴的数据

### 重要性分数计算
- 为每个轴单独计算重要性分数
- 基于渲染权重和投影面积
- 支持室外场景的特殊处理

## 测试

运行测试脚本验证功能：
```bash
python test_sharpness_culling.py
```

测试包括：
- Sharpness剪枝功能测试
- OptimizingSpaSG维度修复测试
- 轴数管理测试

## 技术细节

### 内存管理
- 使用`.clone()`避免张量内存重叠问题
- 正确处理优化器状态的更新
- 支持densification和pruning操作

### 兼容性
- 向后兼容原有的球面高斯模型
- 支持模型的保存和加载
- 保持与原有训练流程的一致性

## 注意事项

1. sharpness阈值需要根据具体场景调整
2. 剪枝频率会影响训练稳定性
3. 建议在训练后期进行剪枝以获得更好的效果
4. 轴数记录在模型保存/加载时需要特别处理
