#!/usr/bin/env python3
"""
测试sharpness剪枝功能的简单脚本
"""

import torch
import numpy as np
from scene.spherical_gaussian_model_cullSG import SphericalGaussianModelcullSG
from optimizing_spa_sg import OptimizingSpaSG
from argparse import Namespace

def test_sharpness_culling():
    """测试sharpness剪枝功能"""
    print("Testing sharpness culling functionality...")
    
    # 创建模型
    max_sg_degree = 3
    gaussians = SphericalGaussianModelcullSG(max_sg_degree)
    
    # 模拟一些点云数据
    num_points = 10
    xyz = torch.randn(num_points, 3, device="cuda")
    rgb = torch.rand(num_points, 3, device="cuda")
    
    # 手动初始化参数
    gaussians._xyz = torch.nn.Parameter(xyz.requires_grad_(True))
    gaussians._rgb_base = torch.nn.Parameter(rgb.requires_grad_(True))
    gaussians._scaling = torch.nn.Parameter(torch.randn(num_points, 3, device="cuda").requires_grad_(True))
    gaussians._rotation = torch.nn.Parameter(torch.randn(num_points, 4, device="cuda").requires_grad_(True))
    gaussians._opacity = torch.nn.Parameter(torch.randn(num_points, 1, device="cuda").requires_grad_(True))
    
    # 初始化球面高斯参数
    gaussians._sg_directions = torch.nn.Parameter(torch.randn(num_points, max_sg_degree, 3, device="cuda").requires_grad_(True))
    gaussians._sg_sharpness = torch.nn.Parameter(torch.randn(num_points, max_sg_degree, 1, device="cuda").requires_grad_(True))
    gaussians._sg_rgb = torch.nn.Parameter(torch.randn(num_points, max_sg_degree, 3, device="cuda").requires_grad_(True))
    
    # 初始化轴数记录
    gaussians._sg_axis_count = torch.full((num_points,), max_sg_degree, device="cuda", dtype=torch.int)
    
    # 设置一些轴的sharpness为很小的值
    with torch.no_grad():
        gaussians._sg_sharpness[0, 0, 0] = 0.005  # 低于阈值
        gaussians._sg_sharpness[1, 1, 0] = 0.008  # 低于阈值
        gaussians._sg_sharpness[2, 2, 0] = 0.003  # 低于阈值
    
    print(f"Before culling:")
    print(f"  Total axes: {gaussians.get_sg_axis_count.sum().item()}")
    print(f"  Axis counts per gaussian: {gaussians.get_sg_axis_count.cpu().numpy()}")
    print(f"  Sample sharpness values: {gaussians.get_sg_sharpness[:3, :, 0].detach().cpu().numpy()}")
    
    # 执行剪枝
    gaussians.cull_low_sharpness_axes(sharpness_threshold=0.01)
    
    print(f"\nAfter culling:")
    print(f"  Total axes: {gaussians.get_sg_axis_count.sum().item()}")
    print(f"  Axis counts per gaussian: {gaussians.get_sg_axis_count.cpu().numpy()}")
    
    print("Sharpness culling test completed successfully!")

def test_optimizing_spa_sg():
    """测试OptimizingSpaSG的维度修复"""
    print("\nTesting OptimizingSpaSG dimension fix...")
    
    # 创建模型
    max_sg_degree = 3
    gaussians = SphericalGaussianModelcullSG(max_sg_degree)
    
    # 模拟一些点云数据
    num_points = 5
    xyz = torch.randn(num_points, 3, device="cuda")
    
    # 手动初始化参数
    gaussians._xyz = torch.nn.Parameter(xyz.requires_grad_(True))
    gaussians._sg_sharpness = torch.nn.Parameter(torch.abs(torch.randn(num_points, max_sg_degree, 1, device="cuda")).requires_grad_(True))
    
    # 创建优化参数
    opt = Namespace()
    opt.rho_lr = 0.01
    opt.prune_ratio2 = 0.3
    
    # 创建OptimizingSpaSG实例
    optimizing_spa_sg = OptimizingSpaSG(gaussians, opt, "cuda", imp_score_flag=True)
    
    # 创建模拟的重要性分数 [N, max_sg_degree]
    imp_score = torch.rand(num_points, max_sg_degree, device="cuda")
    
    print(f"Sharpness shape: {gaussians._sg_sharpness.shape}")
    print(f"Importance score shape: {imp_score.shape}")
    
    # 测试prune_z_metrics_imp_score函数
    z = gaussians.get_sg_sharpness + optimizing_spa_sg.u
    result = optimizing_spa_sg.prune_z_metrics_imp_score(z, imp_score)
    
    print(f"Result shape: {result.shape}")
    print(f"Non-zero elements before: {(z > 0).sum().item()}")
    print(f"Non-zero elements after: {(result > 0).sum().item()}")
    
    print("OptimizingSpaSG test completed successfully!")

if __name__ == "__main__":
    test_sharpness_culling()
    test_optimizing_spa_sg()
    print("\nAll tests passed!")
